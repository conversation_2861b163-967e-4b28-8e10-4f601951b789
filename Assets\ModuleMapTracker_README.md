# Module Map Tracker System

This system tracks modules in the play area and displays them as 3D module pieces on the map at their respective world locations. Unlike ships, only one module piece is shown per player per world, regardless of how many modules they actually have there.

## Setup Instructions

1. **Add ModuleMapTrackerInitializer to the scene:**
   - Create an empty GameObject in your scene
   - Add the `ModuleMapTrackerInitializer` component to it
   - Assign the module prefabs in the inspector:
     - Red Module Prefab: (assign your red module prefab)
     - Green Module Prefab: (assign your green module prefab)
     - Blue Module Prefab: (assign your blue module prefab)
     - Yellow Module Prefab: (assign your yellow module prefab)

2. **The system will automatically:**
   - Create a ModuleMapTracker GameObject in the scene
   - Initialize tracking for all world locations and orbit locations
   - Display module pieces when players acquire modules
   - Update module pieces when modules are added or removed

## How It Works

### Module Positioning
- Modules are positioned in a row under the ships at each world location
- Each player gets one module piece per world (regardless of actual module count)
- Module pieces are spaced horizontally: Player 0 at +1.5, Player 1 at +0.5, etc.
- For regular worlds: modules are placed at Z position = world Z + 2.5 + moduleRowOffset
- For orbit locations: modules are placed at Z position = orbit Z + moduleRowOffset

### Integration
- The system automatically integrates with the Player class
- When `Player.AddModule()` is called, the module tracker is notified
- When `Player.RemoveModule()` is called, the module tracker is notified
- Module pieces are automatically created/destroyed based on module presence

### Key Features
- **Singleton Pattern**: Only one ModuleMapTracker instance per scene
- **Automatic Tracking**: Tracks both regular worlds and orbit locations
- **Player Color Coding**: Each player has their own colored module pieces
- **Simplified Display**: Shows presence of modules, not exact count
- **Dynamic Updates**: Module pieces update in real-time as modules are added/removed

## Configuration

### Settings (in ModuleMapTracker component)
- `trackOrbitLocations`: Enable/disable tracking modules in orbit locations
- `moduleRowOffset`: Distance below ships to place module row (default: 1.5)

### Prefab Requirements
- Module prefabs should be small 3D objects representing modules
- Each player color should have a distinct visual appearance
- Prefabs will be instantiated and positioned automatically

## Technical Details

### Key Classes
- `ModuleMapTracker`: Main tracking system (singleton)
- `ModuleMapTrackerInitializer`: Sets up the tracker and assigns prefabs
- Integration with `Player.AddModule()` and `Player.RemoveModule()`

### Data Structure
- Uses `Dictionary<GameObject, Dictionary<int, GameObject>>` to track module pieces
- Key: World/orbit location GameObject
- Value: Dictionary mapping player ID to their module piece at that location

### Positioning Logic
- Modules positioned relative to world transform positions
- Horizontal spacing between players: 1 unit apart
- Vertical offset below ships: configurable via `moduleRowOffset`
- Supports both regular worlds and orbit locations

## Usage Notes

1. **Module pieces represent presence, not quantity**: If a player has 4 modules at Earth, only 1 module piece is shown
2. **Automatic cleanup**: Module pieces are destroyed when all modules are removed from a location
3. **Performance optimized**: Only creates/destroys pieces when module counts change
4. **Orbit support**: Works with both planetary surfaces and orbit locations
5. **Color consistency**: Uses same color scheme as ships (red, green, blue, yellow)
