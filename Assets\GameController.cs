using UnityEngine;
using UnityEngine.UI;
using System.Collections;
using System.Collections.Generic;
using TMPro;

/// <summary>
/// Main controller for the game flow and UI interactions
/// </summary>
public class GameController : MonoBehaviour
{
    // Singleton pattern
    public static GameController Instance { get; private set; }
    
    [Header("Game Setup")]
    [SerializeField] private int numberOfPlayers = 4;
    [SerializeField] private int actionsPerTurn = 5;
    [SerializeField] private int startingMoney = 10;
    [SerializeField] private int startingScience = 0;
    [SerializeField] private int startingEarthFuel = 10;
    [SerializeField] private int startingEarthOre = 4;
    [SerializeField] private int startingEarthIce = 2;
    [SerializeField] private int startingEarthCarbon = 2;
    [SerializeField] private int startingEarthSilicon = 2;
    [SerializeField] private int startingEarthRareEarth = 1;
    
    [Header("UI References")]
    [SerializeField] private Button endTurnButton;
    [SerializeField] private GameObject gameSetupPanel;
    [SerializeField] private GameObject gameplayUI;
    [SerializeField] private GameObject gameOverPanel;
    
    [Header("Player Area References")]
    [SerializeField] private PlayerPlayArea redPlayerArea;
    [SerializeField] private PlayerPlayArea bluePlayerArea;
    [SerializeField] private PlayerPlayArea greenPlayerArea;
    [SerializeField] private PlayerPlayArea yellowPlayerArea;


    [Header("Action Buttons")]
    [SerializeField] private Button extractButton;
    [SerializeField] private Button buildButton;
    [SerializeField] private Button processButton;
    [SerializeField] private Button moveButton;
    [SerializeField] private Button tradeButton;
    [SerializeField] private Button cancelActionButton;
    
    [Header("Testing")]
    [SerializeField] private bool enableTestMode = false; // Add this field in the inspector
    [SerializeField] private int testOreAmount = 1; // Amount of ore to add in test mode
    [SerializeField] private bool enableTestShips = false; // Add test ships to all locations
    [SerializeField] private int testShipsPerLocation = 2; // Number of super-heavy reusable rockets to add per location

    // Game state
    private bool gameInProgress = false;
    private int currentRound = 1;
    private ActionType currentAction = ActionType.None;
    
    // Selection state
    private GameObject selectedWorld = null;
    private GameObject selectedShip = null;
    private GameObject selectedModule = null;
    private TechnologyCard selectedTechnology = null;
    
    private void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        
        Instance = this;
    }
    
    private void Start()
    {
        // Set up button listeners
        SetupButtonListeners();
        
        // Start in setup mode
        ShowGameSetupPanel();
        
        // Initialize game in the proper order
        InitializeGame();
    }
    
    /// <summary>
    /// Set up button click listeners
    /// </summary>
    private void SetupButtonListeners()
    {
        if (endTurnButton != null)
            endTurnButton.onClick.AddListener(OnEndTurnClicked);
            
        if (extractButton != null)
            extractButton.onClick.AddListener(() => SetCurrentAction(ActionType.Extract));
            
        if (buildButton != null)
            buildButton.onClick.AddListener(() => SetCurrentAction(ActionType.Build));
            
        if (processButton != null)
            processButton.onClick.AddListener(() => SetCurrentAction(ActionType.Process));
            
        if (moveButton != null)
            moveButton.onClick.AddListener(() => SetCurrentAction(ActionType.Move));
            
        if (tradeButton != null)
            tradeButton.onClick.AddListener(() => SetCurrentAction(ActionType.Trade));
            
        if (cancelActionButton != null)
            cancelActionButton.onClick.AddListener(CancelCurrentAction);
    }
    
    /// <summary>
    /// Start a new game
    /// </summary>
    public void StartNewGame()
    {
        // Hide setup panel
        if (gameSetupPanel != null)
            gameSetupPanel.SetActive(false);
            
        // Show gameplay UI
        if (gameplayUI != null)
            gameplayUI.SetActive(true);
            
        // Initialize game managers
        InitializeGame();
        
        // Start first turn
        StartNextTurn();
        
        gameInProgress = true;
        Debug.Log("Game started");
        
        // Add this delay to ensure everything is initialized
        Invoke("ForceUpdatePlayerUIs", 0.5f); // Wait 0.5 seconds to ensure initialization
    }
    
    // Helper method to update science text in a PlayArea
    private void UpdateScienceText(PlayerPlayArea playArea, int scienceValue)
    {
        playArea.scienceText.text = $"{scienceValue}";
        playArea.player.ScienceValue = scienceValue;
        playArea.UpdateInfoDisplay();
    }
    // Helper method to update money text in a PlayArea
    private void UpdateMoneyText(PlayerPlayArea playArea, int moneyValue)
    {
        playArea.moneyText.text = $"{moneyValue}";
        playArea.player.Money = moneyValue;
        playArea.UpdateInfoDisplay();
    }

    /// <summary>
    /// Initialize game managers and state
    /// </summary>
    private void InitializeGame()
    {
        // Initialize GameManager
        GameManager gameManager = GameManager.Instance;
        if (gameManager != null)
        {
            // Configure players with starting resources
            for (int i = 0; i < gameManager.Players.Count; i++)
            {
                Player player = gameManager.Players[i];

                // Set starting values without creating new Player instances
                player.ScienceValue = startingScience;
                player.Money = startingMoney;
                player.MaxActions = actionsPerTurn;

                // Update UI correctly
                PlayerPlayArea playerArea = null;
                if (i == 0) playerArea = redPlayerArea;
                else if (i == 1) playerArea = greenPlayerArea;
                else if (i == 2) playerArea = bluePlayerArea;
                else if (i == 3) playerArea = yellowPlayerArea;

                if (playerArea != null)
                {
                    UpdateScienceText(playerArea, startingScience);
                    UpdateMoneyText(playerArea, startingMoney);
                }
            }
        }
        else
        {
            Debug.LogError("GameManager not found! Stopping initialization.");
            return;
        }

        TechnologyDeckManager deckManager = TechnologyDeckManager.Instance;
        if (deckManager != null && !CheckTechDeckInitialized(deckManager))
        {
            InitializeTechDeck(deckManager);
        }

        // PROPER INITIALIZATION ORDER
        // 1. First, initialize player resources
        InitializePlayerResources();

        // 2. Second, initialize starting tech (don't distribute yet)
        if (deckManager != null)
        {
            // Just initialize the decks without distributing yet
        }

        // 3. Third, add starting ships to each player
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;
        if (playAreaManager != null)
        {
            for (int i = 0; i < numberOfPlayers; i++)
            {
                PlayerPlayArea playerArea = playAreaManager.GetPlayerPlayArea(i);
                if (playerArea != null)
                {
                    playerArea.AddStartingShip();
                }
            }
        }

        // 4. Finally, add starting technologies
        if (playAreaManager != null)
        {
            for (int i = 0; i < numberOfPlayers; i++)
            {
                PlayerPlayArea playerArea = playAreaManager.GetPlayerPlayArea(i);
                if (playerArea != null)
                {
                    playerArea.AddStartingTechCards();
                }
            }
        }

        // 5. Refresh play areas
        if (playAreaManager != null)
        {
            playAreaManager.RefreshAllPlayAreas();
        }

        // TEST MODE: Add 1 ore to every world for each player
        if (enableTestMode)
        {
            AddTestResourcesOnAllWorlds();
        }

        // TEST MODE: Add test ships to every location for each player
        if (enableTestShips)
        {
            AddTestShipsToAllLocations();
        }

        currentRound = 1;
    }

    /// <summary>
    /// Add test resources to all planets and orbit locations for each player
    /// </summary>
    private void AddTestResourcesOnAllWorlds()
    {
        WorldManager worldManager = WorldManager.Instance;
        GameManager gameManager = GameManager.Instance;

        if (worldManager == null || gameManager == null)
            return;

        List<GameObject> allWorlds = worldManager.GetAllCelestialBodies();
        List<GameObject> allOrbits = worldManager.GetAllOrbitLocations();

        foreach (Player player in gameManager.Players)
        {
            // Add resources to all celestial bodies
            foreach (GameObject world in allWorlds)
            {
                player.AddResource(world, ResourceType.Ore, testOreAmount);

                // Update resource display
                ResourceDisplayManager resourceDisplayManager = FindFirstObjectByType<ResourceDisplayManager>();
                if (resourceDisplayManager != null)
                {
                    Dictionary<ResourceType, int> resources = player.GetResourcesOnPlanet(world);
                    resourceDisplayManager.UpdatePlayerResourceDisplay(player.PlayerId, world, resources);
                }
            }

            // Add resources to all orbit locations
            foreach (GameObject orbit in allOrbits)
            {
                player.AddResource(orbit, ResourceType.Ore, testOreAmount);

                // Update resource display
                ResourceDisplayManager resourceDisplayManager = FindFirstObjectByType<ResourceDisplayManager>();
                if (resourceDisplayManager != null)
                {
                    Dictionary<ResourceType, int> resources = player.GetResourcesOnPlanet(orbit);
                    resourceDisplayManager.UpdatePlayerResourceDisplay(player.PlayerId, orbit, resources);
                }
            }
        }
    }

    /// <summary>
    /// Add test ships to all planets and orbit locations for each player
    /// </summary>
    private void AddTestShipsToAllLocations()
    {
        WorldManager worldManager = WorldManager.Instance;
        GameManager gameManager = GameManager.Instance;
        PlayAreaManager playAreaManager = PlayAreaManager.Instance;

        if (worldManager == null || gameManager == null || playAreaManager == null)
            return;

        List<GameObject> allWorlds = worldManager.GetAllCelestialBodies();
        List<GameObject> allOrbits = worldManager.GetAllOrbitLocations();

        foreach (Player player in gameManager.Players)
        {
            PlayerPlayArea playerArea = playAreaManager.GetPlayerPlayArea(player.PlayerId);
            if (playerArea == null) continue;

            // Add ships to all celestial bodies
            foreach (GameObject world in allWorlds)
            {
                AddTestShipsToLocation(player, playerArea, world, testShipsPerLocation);
            }

            // Add ships to all orbit locations
            foreach (GameObject orbit in allOrbits)
            {
                AddTestShipsToLocation(player, playerArea, orbit, testShipsPerLocation);
            }
        }
    }

    /// <summary>
    /// Add a specified number of test ships to a specific location for a player
    /// </summary>
    private void AddTestShipsToLocation(Player player, PlayerPlayArea playerArea, GameObject location, int shipCount)
    {
        for (int i = 0; i < shipCount; i++)
        {
            Ship testShip = new Ship
            {
                Name = "Super-heavy Reusable Rocket",
                CurrentLocation = location,
                DeltaVPerFuel = 4,
                CargoCapacity = 4,
                Strength = 1
            };

            player.AddShip(testShip);
            player.ShipsInPlay++;
            playerArea.AddShipCard(location, testShip);

            // Update ship pieces on map
            if (ShipMapTracker.Instance != null)
            {
                ShipMapTracker.Instance.OnShipAddedToWorld(player.PlayerId, location);
            }
        }
    }

    private void InitializePlayerResources()
    {
        // Get references
        GameManager gameManager = GameManager.Instance;
        WorldManager worldManager = WorldManager.Instance;

        if (gameManager == null || worldManager == null)
        {
            Debug.LogError("GameManager or WorldManager is null in InitializePlayerResources!");
            return;
        }

        GameObject earth = worldManager.GetCelestialBodyByName("Earth");
        if (earth == null)
        {
            Debug.LogError("Earth not found in WorldManager! Cannot initialize resources!");
            return;
        }

        // Give starting resources to each player
        for (int i = 0; i < gameManager.Players.Count; i++)
        {
            if (gameManager.Players[i].HasResourcesAtPlanet(earth))
            {
                gameManager.Players[i].ClearResourcesAtPlanet(earth);
            }

            // Add resources at Earth
            gameManager.Players[i].AddResource(earth, ResourceType.Ore, startingEarthOre);
            gameManager.Players[i].AddResource(earth, ResourceType.Ice, startingEarthIce);
            gameManager.Players[i].AddResource(earth, ResourceType.Carbon, startingEarthCarbon);
            gameManager.Players[i].AddResource(earth, ResourceType.Silicon, startingEarthSilicon);
            gameManager.Players[i].AddResource(earth, ResourceType.RareEarths, startingEarthRareEarth);
            gameManager.Players[i].AddResource(earth, ResourceType.Fuel, startingEarthFuel);

            // Get resources to update display
            Dictionary<ResourceType, int> resources = gameManager.Players[i].GetResourcesOnPlanet(earth);

            // Update resource display
            ResourceDisplayManager resourceDisplayManager = FindFirstObjectByType<ResourceDisplayManager>();
            if (resourceDisplayManager != null)
            {
                resourceDisplayManager.UpdatePlayerResourceDisplay(gameManager.Players[i].PlayerId, earth, resources);
            }
        }
    }

    /// <summary>
    /// Check if the tech deck is initialized
    /// </summary>
    private bool CheckTechDeckInitialized(TechnologyDeckManager deckManager)
    {
        // Instead of accessing IsInitialized property, we'll check if decks have cards
        return deckManager.GetRemainingCardsInCurrentDeck() > 0;
    }
    
    /// <summary>
    /// Initialize the technology deck
    /// </summary>
    private void InitializeTechDeck(TechnologyDeckManager deckManager)
    {
        // This will need to implement or call the initialization logic
        // Since we don't have direct access to InitializeDecks, we'll need to use other methods
        
        // For now, we'll just log a warning that this would happen in a real implementation
        Debug.LogWarning("Technology deck initialization would be called here");
        
        // In a real implementation, you would either:
        // 1. Add an IsInitialized property and InitializeDecks method to TechnologyDeckManager
        // 2. Use an event system to trigger initialization
        // 3. Call setup methods that are already public
    }
    
    /// <summary>
    /// Start the next player's turn
    /// </summary>
    private void StartNextTurn()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        // Get current player
        Player currentPlayer = gameManager.CurrentPlayer;
        int currentPlayerId = gameManager.CurrentPlayerIndex;
        
        if (currentPlayer == null)
            return;
            
        
        // Draw an Earth card
        EarthCardManager earthCardManager = EarthCardManager.Instance;
        if (earthCardManager != null)
        {
            EarthCard drawnCard = earthCardManager.DrawCard();
            
            if (drawnCard == null)
            {
                // No more Earth cards - game is in final rounds
                if (currentPlayerId == 0)
                {
                    Debug.Log("Final round started - no more Earth cards");
                    // Could show UI notification
                }
            }
        }
        
        // Reset action selection
        CancelCurrentAction();
        
        // Update UI buttons
        UpdateActionButtonsState();
        
        Debug.Log($"Player {currentPlayerId + 1}'s turn started");
        
        // Check if this is a new round
        if (currentPlayerId == 0)
        {
            Debug.Log($"Round {currentRound} started");
            currentRound++;
        }
    }
    
    /// <summary>
    /// End the current player's turn
    /// </summary>
    private void OnEndTurnClicked()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        // End the current turn
        gameManager.EndTurn();
        
        // Start the next turn
        StartNextTurn();
        
        // Check for game end
        if (CheckGameEnd())
        {
            EndGame();
        }
    }
    
    /// <summary>
    /// Check if the game should end
    /// </summary>
    private bool CheckGameEnd()
    {
        // Game ends when tier 3 tech deck is empty
        TechnologyDeckManager deckManager = TechnologyDeckManager.Instance;
        if (deckManager != null)
        {
            if (deckManager.GetCurrentTier() == 3 && deckManager.GetRemainingCardsInCurrentDeck() == 0)
            {
                return true;
            }
        }
        
        return false;
    }
    
    /// <summary>
    /// End the game and show the final scores
    /// </summary>
    private void EndGame()
    {
        gameInProgress = false;
        
        // Hide gameplay UI
        if (gameplayUI != null)
            gameplayUI.SetActive(false);
            
        // Show game over panel
        if (gameOverPanel != null)
        {
            gameOverPanel.SetActive(true);
            
            // Update scores in game over panel
            UpdateGameOverScores();
        }
        
        Debug.Log("Game over! Final scores displayed.");
    }
    
    /// <summary>
    /// Update the scores in the game over panel
    /// </summary>
    private void UpdateGameOverScores()
    {
        // This would populate a UI with final scores
        // For now, just log the scores
        
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        // Calculate final scores
        List<PlayerScore> scores = new List<PlayerScore>();
        
        foreach (Player player in gameManager.Players)
        {
            int totalVP = player.CalculateVictoryPoints();
            scores.Add(new PlayerScore { PlayerId = player.PlayerId, Score = totalVP });
        }
        
        // Sort by score (descending)
        scores.Sort((a, b) => b.Score.CompareTo(a.Score));
        
        // Log final scores
        Debug.Log("Final Scores:");
        foreach (PlayerScore score in scores)
        {
            Debug.Log($"Player {score.PlayerId + 1}: {score.Score} VP");
        }
        
        // Determine the winner
        if (scores.Count > 0)
        {
            Debug.Log($"Player {scores[0].PlayerId + 1} wins with {scores[0].Score} VP!");
        }
    }
    
    /// <summary>
    /// Set the current action type
    /// </summary>
    private void SetCurrentAction(ActionType actionType)
    {
        // Cancel current action if selecting the same one
        if (actionType == currentAction)
        {
            CancelCurrentAction();
            return;
        }
        
        // Set new action
        currentAction = actionType;
        
        // Update button states
        UpdateActionButtonsState();
        
        // Clear selections
        ClearSelections();
        
        // Update UI feedback
        switch (actionType)
        {
            case ActionType.Extract:
                Debug.Log("Select a location with a mining module to mine resources.");
                break;
                
            case ActionType.Build:
                Debug.Log("Select a location and then a blueprint to build.");
                break;
                
            case ActionType.Process:
                Debug.Log("Select a location with a processor module to process resources.");
                break;
                
            case ActionType.Move:
                Debug.Log("Select a ship and then a destination to move to.");
                break;
                
            case ActionType.Trade:
                Debug.Log("Select resources to buy or sell at Earth's market.");
                break;
        }
        
        // Show cancel button
        if (cancelActionButton != null)
            cancelActionButton.gameObject.SetActive(true);
    }
    
    /// <summary>
    /// Cancel the current action selection
    /// </summary>
    private void CancelCurrentAction()
    {
        currentAction = ActionType.None;
        
        // Clear selections
        ClearSelections();
        
        // Update button states
        UpdateActionButtonsState();
        
        // Hide cancel button
        if (cancelActionButton != null)
            cancelActionButton.gameObject.SetActive(false);
    }
    
    /// <summary>
    /// Update the state of action buttons based on current game state
    /// </summary>
    private void UpdateActionButtonsState()
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        // Disable all action buttons if no actions remaining
        bool canTakeAction = gameManager.ActionsRemaining > 0;
        
        if (extractButton != null)
            extractButton.interactable = canTakeAction && currentAction != ActionType.Extract;
            
        if (buildButton != null)
            buildButton.interactable = canTakeAction && currentAction != ActionType.Build;
            
        if (processButton != null)
            processButton.interactable = canTakeAction && currentAction != ActionType.Process;
            
        if (moveButton != null)
            moveButton.interactable = canTakeAction && currentAction != ActionType.Move;
            
        if (tradeButton != null)
            tradeButton.interactable = canTakeAction && currentAction != ActionType.Trade;
            
        // End turn button always enabled
        if (endTurnButton != null)
            endTurnButton.interactable = true;
    }
    
    /// <summary>
    /// Clear all current selections
    /// </summary>
    private void ClearSelections()
    {
        selectedWorld = null;
        selectedShip = null;
        selectedModule = null;
        selectedTechnology = null;
        
        // Could add visual feedback to show selections are cleared
    }
    
    /// <summary>
    /// Show the game setup panel
    /// </summary>
    private void ShowGameSetupPanel()
    {
        /*
        if (gameSetupPanel != null)
            gameSetupPanel.SetActive(true);
            
        if (gameplayUI != null)
            gameplayUI.SetActive(false);
            
        if (gameOverPanel != null)
            gameOverPanel.SetActive(false);
        */
        // This would show the game setup UI for player selection, etc.
    }
    
    /// <summary>
    /// Handle a world being selected
    /// </summary>
    public void OnWorldSelected(GameObject world)
    {
        if (!gameInProgress || world == null)
            return;
            
        // Store the selection
        selectedWorld = world;
        
        // Handle based on current action
        switch (currentAction)
        {
            case ActionType.Extract:
                // Try to extract at this location
                ExtractAtLocation(world);
                break;
                
            case ActionType.Build:
                // Show build options for this location
                ShowBuildOptions(world);
                break;
                
            case ActionType.Process:
                // Show processor options for this location
                ShowProcessorOptions(world);
                break;
                
            case ActionType.Move:
                if (selectedShip != null)
                {
                    // Move the selected ship to this destination
                    MoveShipToLocation(selectedShip, world);
                }
                else
                {
                    // Show ships at this location
                    ShowShipsAtLocation(world);
                }
                break;
                
            case ActionType.Trade:
                // Can only trade at Earth
                PlanetBody planetBody = world.GetComponent<PlanetBody>();
                if (planetBody != null && planetBody.Name == "Earth")
                {
                    ShowTradeOptions();
                }
                else
                {
                    Debug.LogWarning("Trading is only available at Earth");
                }
                break;
        }
    }
    
    /// <summary>
    /// Handle a ship being selected
    /// </summary>
    public void OnShipSelected(GameObject ship)
    {
        if (!gameInProgress || ship == null)
            return;
            
        // Store the selection
        selectedShip = ship;
        
        // Handle based on current action
        if (currentAction == ActionType.Move)
        {
            Debug.Log($"Ship {ship.name} selected. Now select a destination.");
        }
    }
    
    /// <summary>
    /// Handle a module being selected
    /// </summary>
    public void OnModuleSelected(GameObject module)
    {
        if (!gameInProgress || module == null)
            return;
            
        // Store the selection
        selectedModule = module;
        
        // Handle based on current action
        if (currentAction == ActionType.Process)
        {
            ProcessWithModule(module);
        }
    }
    
    /// <summary>
    /// Handle a technology card being selected
    /// </summary>
    public void OnTechnologySelected(TechnologyCard technology)
    {
        if (!gameInProgress || technology == null)
            return;
            
        // Store the selection
        selectedTechnology = technology;
        
        // Handle based on current action
        if (currentAction == ActionType.Build && technology is BlueprintTechnologyCard)
        {
            if (selectedWorld != null)
            {
                BuildWithBlueprint(selectedWorld, technology as BlueprintTechnologyCard);
            }
            else
            {
                Debug.Log("Select a location to build at first.");
            }
        }
    }

    #region Action Implementation Methods
    
    /// <summary>
    /// Extract resources at a location
    /// </summary>
    private void ExtractAtLocation(GameObject location)
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        int currentPlayerId = gameManager.CurrentPlayerIndex;
        
        // Use the PlayerActions component to extract
        PlayerActions playerActions = PlayerActions.Instance;
        if (playerActions != null)
        {
            bool success = playerActions.ExtractLocation(currentPlayerId, location);
            
            if (success)
            {
                
                // Reset action selection
                CancelCurrentAction();
                
                // Update action buttons
                UpdateActionButtonsState();
            }
        }
    }
    
    /// <summary>
    /// Show build options for a location
    /// </summary>
    private void ShowBuildOptions(GameObject location)
    {
        // This would show a UI with available blueprints to build at this location
        Debug.Log($"Showing build options for {location.name}");
        
        // Get current player's blueprints
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        Player currentPlayer = gameManager.CurrentPlayer;
        if (currentPlayer == null)
            return;
            
        List<TechnologyCardData> technologies = currentPlayer.GetTechnologies();
        List<BlueprintTechnologyCardData> blueprints = new List<BlueprintTechnologyCardData>();
        
        foreach (TechnologyCardData tech in technologies)
        {
            if (tech is BlueprintTechnologyCardData)
            {
                blueprints.Add(tech as BlueprintTechnologyCardData);
            }
        }
        
        // For this example, just log the available blueprints
        Debug.Log($"Available blueprints for {location.name}:");
        foreach (BlueprintTechnologyCardData blueprint in blueprints)
        {
            Debug.Log($"- {blueprint.cardName}");
        }
        
        // In a real implementation, this would open a UI to select a blueprint
    }
    
    /// <summary>
    /// Show processor options for a location
    /// </summary>
    private void ShowProcessorOptions(GameObject location)
    {
        // This would show a UI with available processor modules at this location
        Debug.Log($"Showing processor options for {location.name}");
        
        // Get current player's processors at this location
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        Player currentPlayer = gameManager.CurrentPlayer;
        if (currentPlayer == null)
            return;
            
        List<Module> modules = currentPlayer.GetModulesOnPlanet(location);
        List<Module> processors = new List<Module>();
        
        foreach (Module module in modules)
        {
            if (module.Type == ModuleType.Processor)
            {
                processors.Add(module);
            }
        }
        
        // For this example, just log the available processors
        Debug.Log($"Available processors at {location.name}:");
        foreach (Module processor in processors)
        {
            Debug.Log($"- {processor.Name}");
        }
        
        // In a real implementation, this would open a UI to select a processor
    }
    
    /// <summary>
    /// Show ships at a location for movement
    /// </summary>
    private void ShowShipsAtLocation(GameObject location)
    {
        // This would show a UI with available ships at this location
        Debug.Log($"Showing ships at {location.name}");
        
        // Get current player's ships at this location
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        Player currentPlayer = gameManager.CurrentPlayer;
        if (currentPlayer == null)
            return;
            
        List<Ship> ships = currentPlayer.GetShipsAtLocation(location);
        
        // For this example, just log the available ships
        Debug.Log($"Available ships at {location.name}:");
        foreach (Ship ship in ships)
        {
            Debug.Log($"- {ship.Name}");
        }
        
        // In a real implementation, this would open a UI to select a ship
    }
    
    /// <summary>
    /// Show trade options at Earth
    /// </summary>
    private void ShowTradeOptions()
    {
        // This would show a UI with trade options
        Debug.Log("Showing trade options");
        
        // In a real implementation, this would open a UI to select resources to buy/sell
    }
    
    /// <summary>
    /// Move a ship to a location
    /// </summary>
    private void MoveShipToLocation(GameObject shipObject, GameObject destination)
    {
        // Get the ship component
        Ship ship = shipObject.GetComponent<Ship>();
        if (ship == null)
            return;
            
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        int currentPlayerId = gameManager.CurrentPlayerIndex;
        
        // Use the PlayerActions component to move the ship
        PlayerActions playerActions = PlayerActions.Instance;
        if (playerActions != null)
        {
            bool success = playerActions.MoveShip(currentPlayerId, ship, destination);
            
            if (success)
            {
                // Reset action selection
                CancelCurrentAction();
                
                // Update action buttons
                UpdateActionButtonsState();
            }
        }
    }
    
    /// <summary>
    /// Process resources with a module
    /// </summary>
    private void ProcessWithModule(GameObject moduleObject)
    {
        // Get the module component
        Module module = moduleObject.GetComponent<Module>();
        if (module == null)
            return;
            
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        int currentPlayerId = gameManager.CurrentPlayerIndex;
        
        // Use the PlayerActions component to process resources
        PlayerActions playerActions = PlayerActions.Instance;
        if (playerActions != null)
        {
            // TODO: Get the module index from the module list
            int moduleIndex = 0;
            
            bool success = playerActions.ProcessResources(currentPlayerId, selectedWorld, moduleIndex);
            
            if (success)
            {
                // Reset action selection
                CancelCurrentAction();
                
                // Update action buttons
                UpdateActionButtonsState();
            }
        }
    }
    
    /// <summary>
    /// Build with a blueprint at a location
    /// </summary>
    private void BuildWithBlueprint(GameObject location, BlueprintTechnologyCard blueprint)
    {
        GameManager gameManager = GameManager.Instance;
        if (gameManager == null)
            return;
            
        int currentPlayerId = gameManager.CurrentPlayerIndex;
        
        // Use the PlayerActions component to build
        PlayerActions playerActions = PlayerActions.Instance;
        if (playerActions != null)
        {
            bool success = false;
            
            if (blueprint.Type == BlueprintType.Ship)
            {
                // Convert BlueprintTechnologyCard to TechnologyCardData
                TechnologyCardData techData = blueprint.GetDataCopy();
                success = playerActions.BuildShip(currentPlayerId, location, techData);
            }
            else
            {
                // Convert BlueprintTechnologyCard to TechnologyCardData
                TechnologyCardData techData = blueprint.GetDataCopy();
                success = playerActions.BuildStructure(currentPlayerId, location, techData);
            }
            
            if (success)
            {
                // Reset action selection
                CancelCurrentAction();
                
                // Update action buttons
                UpdateActionButtonsState();
            }
        }
    }
    
    #endregion
}

/// <summary>
/// Types of player actions
/// </summary>
public enum ActionType
{
    None,
    Extract,
    Build,
    Process,
    Move,
    Trade,
    TakeTechnology,
    DevelopTechnology
}

/// <summary>
/// Helper class for tracking player scores
/// </summary>
[System.Serializable]
public class PlayerScore
{
    public int PlayerId;
    public int Score;
}